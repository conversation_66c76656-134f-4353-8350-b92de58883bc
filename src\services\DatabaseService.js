const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs-extra');

class DatabaseService {
    constructor() {
        this.dbPath = path.join(__dirname, '../data/printserver.db');
        this.db = null;
    }

    /**
     * 初始化数据库
     */
    async init() {
        try {
            // 确保数据目录存在
            await fs.ensureDir(path.dirname(this.dbPath));
            
            // 连接数据库
            this.db = new sqlite3.Database(this.dbPath);
            
            // 创建表
            await this.createTables();
            
            console.log('数据库初始化成功');
        } catch (error) {
            console.error('数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建数据表
     */
    async createTables() {
        return new Promise((resolve, reject) => {
            const queries = [
                // 打印机配置表
                `CREATE TABLE IF NOT EXISTS printer_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    display_name TEXT,
                    is_default INTEGER DEFAULT 0,
                    settings TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )`,
                
                // 打印任务历史表
                `CREATE TABLE IF NOT EXISTS print_jobs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    job_id TEXT UNIQUE NOT NULL,
                    printer_name TEXT NOT NULL,
                    file_name TEXT,
                    file_type TEXT,
                    copies INTEGER DEFAULT 1,
                    status TEXT DEFAULT 'pending',
                    error_message TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    completed_at DATETIME
                )`,
                
                // 系统设置表
                `CREATE TABLE IF NOT EXISTS system_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    description TEXT,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )`
            ];

            let completed = 0;
            const total = queries.length;

            queries.forEach(query => {
                this.db.run(query, (err) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    
                    completed++;
                    if (completed === total) {
                        resolve();
                    }
                });
            });
        });
    }

    /**
     * 设置默认打印机
     */
    async setDefaultPrinter(printerName) {
        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                // 清除所有默认设置
                this.db.run('UPDATE printer_configs SET is_default = 0', (err) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    
                    // 设置新的默认打印机
                    this.db.run(
                        `INSERT OR REPLACE INTO printer_configs (name, is_default, updated_at) 
                         VALUES (?, 1, CURRENT_TIMESTAMP)`,
                        [printerName],
                        (err) => {
                            if (err) {
                                reject(err);
                            } else {
                                resolve();
                            }
                        }
                    );
                });
            });
        });
    }

    /**
     * 获取默认打印机
     */
    async getDefaultPrinter() {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT name FROM printer_configs WHERE is_default = 1 LIMIT 1',
                (err, row) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(row ? row.name : null);
                    }
                }
            );
        });
    }

    /**
     * 保存打印任务记录
     */
    async savePrintJob(jobData) {
        return new Promise((resolve, reject) => {
            const {
                jobId,
                printerName,
                fileName,
                fileType,
                copies,
                status = 'pending'
            } = jobData;

            this.db.run(
                `INSERT INTO print_jobs (job_id, printer_name, file_name, file_type, copies, status)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [jobId, printerName, fileName, fileType, copies, status],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(this.lastID);
                    }
                }
            );
        });
    }

    /**
     * 更新打印任务状态
     */
    async updatePrintJobStatus(jobId, status, errorMessage = null) {
        return new Promise((resolve, reject) => {
            const completedAt = status === 'completed' || status === 'failed' ? 
                new Date().toISOString() : null;

            this.db.run(
                `UPDATE print_jobs 
                 SET status = ?, error_message = ?, completed_at = ?
                 WHERE job_id = ?`,
                [status, errorMessage, completedAt, jobId],
                (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve();
                    }
                }
            );
        });
    }

    /**
     * 获取打印任务历史
     */
    async getPrintJobHistory(limit = 100) {
        return new Promise((resolve, reject) => {
            this.db.all(
                `SELECT * FROM print_jobs 
                 ORDER BY created_at DESC 
                 LIMIT ?`,
                [limit],
                (err, rows) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(rows);
                    }
                }
            );
        });
    }

    /**
     * 获取系统设置
     */
    async getSetting(key) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT value FROM system_settings WHERE key = ?',
                [key],
                (err, row) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(row ? row.value : null);
                    }
                }
            );
        });
    }

    /**
     * 设置系统配置
     */
    async setSetting(key, value, description = null) {
        return new Promise((resolve, reject) => {
            this.db.run(
                `INSERT OR REPLACE INTO system_settings (key, value, description, updated_at)
                 VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
                [key, value, description],
                (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve();
                    }
                }
            );
        });
    }

    /**
     * 清理过期的打印任务记录
     */
    async cleanupOldJobs(daysToKeep = 30) {
        return new Promise((resolve, reject) => {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            this.db.run(
                'DELETE FROM print_jobs WHERE created_at < ?',
                [cutoffDate.toISOString()],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(this.changes);
                    }
                }
            );
        });
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = DatabaseService;
