{"name": "print-server", "version": "1.0.0", "description": "Web端静默打印服务", "main": "src/main.js", "homepage": "./", "scripts": {"start": "node src/server.js", "dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "nodemon src/server.js", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "build:electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "postinstall": "cd client && npm install"}, "keywords": ["print", "server", "web", "silent-print"], "author": "PrintServer", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "sqlite3": "^5.1.6", "printer": "^0.4.0", "pdf2pic": "^3.0.1", "sharp": "^0.32.6", "fs-extra": "^11.1.1", "path": "^0.12.7", "body-parser": "^1.20.2", "helmet": "^7.1.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "concurrently": "^8.2.2", "electron": "^27.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.printserver.app", "productName": "打印服务器", "directories": {"output": "dist"}, "files": ["src/**/*", "client/dist/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "打印服务器", "installerLanguages": ["zh_CN"], "language": "2052"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}