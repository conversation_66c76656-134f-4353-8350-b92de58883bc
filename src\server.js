const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs-extra');
const helmet = require('helmet');
const compression = require('compression');
const bodyParser = require('body-parser');

const PrinterService = require('./services/PrinterService');
const DatabaseService = require('./services/DatabaseService');

class PrintServer {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3001;
        this.printerService = new PrinterService();
        this.dbService = new DatabaseService();
        
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
    }

    setupMiddleware() {
        // 安全中间件
        this.app.use(helmet({
            contentSecurityPolicy: false
        }));
        
        // 压缩中间件
        this.app.use(compression());
        
        // CORS配置
        this.app.use(cors({
            origin: ['http://localhost:5173', 'http://localhost:3000'],
            credentials: true
        }));
        
        // 解析请求体
        this.app.use(bodyParser.json({ limit: '50mb' }));
        this.app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));
        
        // 静态文件服务
        this.app.use(express.static(path.join(__dirname, '../client/dist')));
        
        // 文件上传配置
        const storage = multer.diskStorage({
            destination: (req, file, cb) => {
                const uploadDir = path.join(__dirname, '../uploads');
                fs.ensureDirSync(uploadDir);
                cb(null, uploadDir);
            },
            filename: (req, file, cb) => {
                const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
                cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
            }
        });
        
        this.upload = multer({ 
            storage: storage,
            limits: {
                fileSize: 50 * 1024 * 1024 // 50MB
            }
        });
    }

    setupRoutes() {
        // API路由
        this.app.use('/api', this.createApiRoutes());
        
        // 前端路由 - 所有其他请求都返回index.html
        this.app.get('*', (req, res) => {
            res.sendFile(path.join(__dirname, '../client/dist/index.html'));
        });
    }

    createApiRoutes() {
        const router = express.Router();

        // 获取所有打印机
        router.get('/printers', async (req, res) => {
            try {
                const printers = await this.printerService.getPrinters();
                res.json({ success: true, data: printers });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });

        // 获取默认打印机
        router.get('/printers/default', async (req, res) => {
            try {
                const defaultPrinter = await this.printerService.getDefaultPrinter();
                res.json({ success: true, data: defaultPrinter });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });

        // 设置默认打印机
        router.post('/printers/default', async (req, res) => {
            try {
                const { printerName } = req.body;
                await this.dbService.setDefaultPrinter(printerName);
                res.json({ success: true, message: '默认打印机设置成功' });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });

        // 打印文件
        router.post('/print', this.upload.single('file'), async (req, res) => {
            try {
                const { printerName, copies = 1, options = {} } = req.body;
                const file = req.file;
                
                if (!file) {
                    return res.status(400).json({ success: false, error: '未提供文件' });
                }

                const result = await this.printerService.printFile(
                    file.path, 
                    printerName, 
                    parseInt(copies),
                    JSON.parse(options || '{}')
                );
                
                // 清理临时文件
                await fs.remove(file.path);
                
                res.json({ success: true, data: result });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });

        // 打印文本
        router.post('/print/text', async (req, res) => {
            try {
                const { text, printerName, copies = 1, options = {} } = req.body;
                
                if (!text) {
                    return res.status(400).json({ success: false, error: '未提供文本内容' });
                }

                const result = await this.printerService.printText(
                    text, 
                    printerName, 
                    parseInt(copies),
                    options
                );
                
                res.json({ success: true, data: result });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });

        // 获取打印任务状态
        router.get('/print/status/:jobId', async (req, res) => {
            try {
                const { jobId } = req.params;
                const status = await this.printerService.getJobStatus(jobId);
                res.json({ success: true, data: status });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });

        // 健康检查
        router.get('/health', (req, res) => {
            res.json({ 
                success: true, 
                message: '打印服务运行正常',
                timestamp: new Date().toISOString()
            });
        });

        return router;
    }

    setupErrorHandling() {
        // 404处理
        this.app.use((req, res) => {
            res.status(404).json({ success: false, error: '接口不存在' });
        });

        // 全局错误处理
        this.app.use((err, req, res, next) => {
            console.error('服务器错误:', err);
            res.status(500).json({ 
                success: false, 
                error: '服务器内部错误',
                details: process.env.NODE_ENV === 'development' ? err.message : undefined
            });
        });
    }

    async start() {
        try {
            // 初始化数据库
            await this.dbService.init();
            
            // 启动服务器
            this.app.listen(this.port, () => {
                console.log(`打印服务器已启动: http://localhost:${this.port}`);
                console.log(`API接口地址: http://localhost:${this.port}/api`);
            });
        } catch (error) {
            console.error('启动服务器失败:', error);
            process.exit(1);
        }
    }
}

// 启动服务器
if (require.main === module) {
    const server = new PrintServer();
    server.start();
}

module.exports = PrintServer;
