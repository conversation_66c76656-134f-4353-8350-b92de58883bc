const printer = require('printer');
const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');
const sharp = require('sharp');

class PrinterService {
    constructor() {
        this.jobs = new Map(); // 存储打印任务状态
    }

    /**
     * 获取所有可用打印机
     */
    async getPrinters() {
        try {
            const printers = printer.getPrinters();
            return printers.map(p => ({
                name: p.name,
                displayName: p.displayName || p.name,
                status: p.status,
                isDefault: p.isDefault,
                attributes: p.attributes,
                location: p.location,
                comment: p.comment
            }));
        } catch (error) {
            console.error('获取打印机列表失败:', error);
            throw new Error('无法获取打印机列表');
        }
    }

    /**
     * 获取默认打印机
     */
    async getDefaultPrinter() {
        try {
            const printers = printer.getPrinters();
            const defaultPrinter = printers.find(p => p.isDefault);
            return defaultPrinter ? {
                name: defaultPrinter.name,
                displayName: defaultPrinter.displayName || defaultPrinter.name,
                status: defaultPrinter.status
            } : null;
        } catch (error) {
            console.error('获取默认打印机失败:', error);
            throw new Error('无法获取默认打印机');
        }
    }

    /**
     * 打印文件
     */
    async printFile(filePath, printerName, copies = 1, options = {}) {
        try {
            const fileExt = path.extname(filePath).toLowerCase();
            const jobId = this.generateJobId();
            
            // 设置打印任务状态
            this.jobs.set(jobId, {
                id: jobId,
                status: 'processing',
                file: path.basename(filePath),
                printer: printerName,
                copies: copies,
                createdAt: new Date()
            });

            let printData;
            let printOptions = {
                printer: printerName,
                copies: copies,
                ...options
            };

            switch (fileExt) {
                case '.pdf':
                    printData = await this.preparePdfForPrint(filePath);
                    printOptions.type = 'PDF';
                    break;
                case '.jpg':
                case '.jpeg':
                case '.png':
                case '.bmp':
                case '.gif':
                    printData = await this.prepareImageForPrint(filePath);
                    printOptions.type = 'RAW';
                    break;
                case '.txt':
                    printData = await fs.readFile(filePath, 'utf8');
                    printOptions.type = 'TEXT';
                    break;
                default:
                    throw new Error(`不支持的文件格式: ${fileExt}`);
            }

            // 执行打印
            const result = await this.executePrint(printData, printOptions);
            
            // 更新任务状态
            this.jobs.set(jobId, {
                ...this.jobs.get(jobId),
                status: 'completed',
                result: result,
                completedAt: new Date()
            });

            return {
                jobId: jobId,
                message: '打印任务已提交',
                printer: printerName,
                copies: copies
            };

        } catch (error) {
            console.error('打印文件失败:', error);
            throw new Error(`打印失败: ${error.message}`);
        }
    }

    /**
     * 打印文本
     */
    async printText(text, printerName, copies = 1, options = {}) {
        try {
            const jobId = this.generateJobId();
            
            this.jobs.set(jobId, {
                id: jobId,
                status: 'processing',
                content: text.substring(0, 50) + '...',
                printer: printerName,
                copies: copies,
                createdAt: new Date()
            });

            const printOptions = {
                printer: printerName,
                type: 'TEXT',
                copies: copies,
                ...options
            };

            const result = await this.executePrint(text, printOptions);
            
            this.jobs.set(jobId, {
                ...this.jobs.get(jobId),
                status: 'completed',
                result: result,
                completedAt: new Date()
            });

            return {
                jobId: jobId,
                message: '文本打印任务已提交',
                printer: printerName,
                copies: copies
            };

        } catch (error) {
            console.error('打印文本失败:', error);
            throw new Error(`文本打印失败: ${error.message}`);
        }
    }

    /**
     * 准备PDF文件打印
     */
    async preparePdfForPrint(filePath) {
        // 对于PDF文件，直接返回文件路径，让系统处理
        return filePath;
    }

    /**
     * 准备图片文件打印
     */
    async prepareImageForPrint(filePath) {
        try {
            // 使用sharp处理图片，确保格式兼容
            const processedPath = filePath.replace(path.extname(filePath), '_processed.jpg');
            
            await sharp(filePath)
                .jpeg({ quality: 90 })
                .toFile(processedPath);
                
            return processedPath;
        } catch (error) {
            console.error('图片处理失败:', error);
            return filePath; // 如果处理失败，使用原文件
        }
    }

    /**
     * 执行打印
     */
    async executePrint(data, options) {
        return new Promise((resolve, reject) => {
            try {
                if (options.type === 'PDF') {
                    // PDF文件使用系统默认程序打印
                    this.printPdfFile(data, options, resolve, reject);
                } else {
                    // 其他类型使用node-printer
                    printer.printDirect({
                        data: data,
                        printer: options.printer,
                        type: options.type,
                        success: (jobID) => {
                            resolve({ jobID, message: '打印任务已发送到打印机' });
                        },
                        error: (err) => {
                            reject(new Error(`打印失败: ${err}`));
                        }
                    });
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 打印PDF文件
     */
    printPdfFile(filePath, options, resolve, reject) {
        try {
            const platform = process.platform;
            let command;

            switch (platform) {
                case 'win32':
                    // Windows使用SumatraPDF或默认PDF阅读器静默打印
                    command = `powershell -Command "Start-Process -FilePath '${filePath}' -ArgumentList '/t','${options.printer}' -WindowStyle Hidden"`;
                    break;
                case 'darwin':
                    // macOS使用lpr命令
                    command = `lpr -P "${options.printer}" "${filePath}"`;
                    break;
                case 'linux':
                    // Linux使用lpr命令
                    command = `lpr -P "${options.printer}" "${filePath}"`;
                    break;
                default:
                    throw new Error(`不支持的操作系统: ${platform}`);
            }

            execSync(command, { stdio: 'pipe' });
            resolve({ message: 'PDF打印任务已发送' });
        } catch (error) {
            reject(new Error(`PDF打印失败: ${error.message}`));
        }
    }

    /**
     * 获取打印任务状态
     */
    async getJobStatus(jobId) {
        const job = this.jobs.get(jobId);
        if (!job) {
            throw new Error('打印任务不存在');
        }
        return job;
    }

    /**
     * 生成任务ID
     */
    generateJobId() {
        return 'job_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 清理过期任务
     */
    cleanupOldJobs() {
        const now = new Date();
        const maxAge = 24 * 60 * 60 * 1000; // 24小时

        for (const [jobId, job] of this.jobs.entries()) {
            if (now - job.createdAt > maxAge) {
                this.jobs.delete(jobId);
            }
        }
    }
}

module.exports = PrinterService;
